"use client";

import { useMemo, useState } from "react";
import { toast } from "sonner";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "lucide-react";
import { canDeleteSession } from "@/lib/flex-packages-utils";
import { deleteSession } from "@/lib/actions/database";

import StatsGrid from "@/components/appointments/StatsGrid";
import PackagesSection from "@/components/appointments/PackagesSection";
import CompletedSessionsTimeline from "@/components/appointments/CompletedSessionsTimeline";
import ModernDeleteDialog from "@/components/appointments/ModernDeleteDialog";
import type {
  Member,
  MemberFlexPackageWithDetails,
} from "@/lib/supabase/types";

// Types mirrored from the server page
export interface ServerFlexPackageSessionWithDetails {
  id: string;
  member_id: string;
  member_package_id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  notes?: string | null;
  created_at: string;
  updated_at: string;
  member_package?: {
    flex_package?: {
      id: string;
      name: string;
      description: string | null;
      duration_days: number;
      session_count: number;
      price: number;
      is_active: boolean;
    } | null;
  } | null;
  member?: Member | null;
  packageName?: string;
}

export interface FlexPackageGroup {
  packageName: string;
  sessions: ServerFlexPackageSessionWithDetails[];
  memberPackage: MemberFlexPackageWithDetails;
  totalSessions: number;
  usedSessions: number;
  remainingSessions: number;
  expiryDate: string;
}

export interface FlexDashboardStats {
  totalSessions: number;
  usedSessions: number;
  remainingSessions: number;
  scheduledSessions: number;
  completedSessions: number;
  activePackages: number;
}

interface FlexDashboardProps {
  initialFlexPackages: FlexPackageGroup[];
  initialStats: FlexDashboardStats;
}

const staggerChildren = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const formatDate = (dateString: string) =>
  new Date(dateString).toLocaleDateString("tr-TR", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });

const formatTime = (timeString: string) => timeString.slice(0, 5);

export default function FlexDashboard({
  initialFlexPackages,
  initialStats,
}: FlexDashboardProps) {
  const [flexPackages, setFlexPackages] =
    useState<FlexPackageGroup[]>(initialFlexPackages);
  const [stats, setStats] = useState<FlexDashboardStats>(initialStats);
  const [expandedPackages, setExpandedPackages] = useState<
    Record<string, boolean>
  >({});
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    show: boolean;
    sessionId: string;
    sessionInfo?: string;
  }>({ show: false, sessionId: "", sessionInfo: "" });

  const togglePackageExpansion = (packageId: string) => {
    setExpandedPackages((prev) => ({ ...prev, [packageId]: !prev[packageId] }));
  };

  const handleDeleteSession = (
    sessionId: string,
    sessionDate: string,
    sessionTime: string
  ) => {
    if (!canDeleteSession(sessionDate, sessionTime)) {
      toast.error("Randevu saatine 12 saatten az kaldığı için silinemez.");
      return;
    }
    setDeleteConfirmation({
      show: true,
      sessionId,
      sessionInfo: `${formatDate(sessionDate)} - ${formatTime(sessionTime)}`,
    });
  };

  const confirmDeleteSession = async () => {
    try {
      const success = await deleteSession(deleteConfirmation.sessionId);
      if (success) {
        toast.success("Randevu başarıyla silindi.");
        // Optimistic local update
        setFlexPackages((prev) => {
          const updated = prev.map((pkg) => ({
            ...pkg,
            sessions: pkg.sessions.filter(
              (s) => s.id !== deleteConfirmation.sessionId
            ),
          }));
          // Recompute stats to keep UI consistent
          const newStats = updated.reduce(
            (acc, p) => {
              acc.totalSessions += p.totalSessions;
              acc.usedSessions += p.usedSessions;
              acc.remainingSessions += p.remainingSessions;
              acc.scheduledSessions += p.sessions.filter(
                (s) => s.status === "scheduled"
              ).length;
              acc.completedSessions += p.sessions.filter(
                (s) => s.status === "completed"
              ).length;
              return acc;
            },
            {
              totalSessions: 0,
              usedSessions: 0,
              remainingSessions: 0,
              scheduledSessions: 0,
              completedSessions: 0,
              activePackages: 0,
            }
          );
          setStats(newStats);
          return updated;
        });
      } else {
        toast.error("Randevu silinirken bir hata oluştu.");
      }
    } catch (error) {
      console.error("Error deleting session:", error);
      toast.error("Randevu silinirken bir hata oluştu.");
    } finally {
      setDeleteConfirmation({ show: false, sessionId: "", sessionInfo: "" });
    }
  };

  // Completed sessions memo
  const allCompletedSessions = useMemo(() => {
    return flexPackages
      .flatMap((p) =>
        p.sessions
          .filter((s) => s.status === "completed")
          .map((s) => ({ ...s, packageName: p.packageName }))
      )
      .sort(
        (a, b) =>
          new Date(b.session_date).getTime() -
          new Date(a.session_date).getTime()
      );
  }, [flexPackages]);

  return (
    <>
      <div className="space-y-6 py-4">
        <motion.div
          initial="initial"
          animate="animate"
          variants={staggerChildren}
          className="space-y-8"
        >
          <h1 className="text-2xl font-bold">Randevularım</h1>
          {/* Active Packages */}
          <PackagesSection
            flexPackages={flexPackages}
            expandedPackages={expandedPackages}
            togglePackageExpansion={togglePackageExpansion}
            handleDeleteSession={handleDeleteSession}
          />

          {/* Completed Sessions */}
          {allCompletedSessions.length > 0 && (
            <CompletedSessionsTimeline sessions={allCompletedSessions} />
          )}
          <StatsGrid stats={stats} />
        </motion.div>
      </div>

      {/* Floating Action Button for New Appointment - Desktop Only */}
      <div className="hidden md:block fixed bottom-8 right-8 z-40">
        <Link href="/appointments/new">
          <Button
            size="lg"
            className="rounded-full w-16 h-16 p-0 shadow-xl hover:shadow-2xl transition-all duration-300 bg-gradient-to-r from-primary to-primary/80"
          >
            <Calendar className="w-7 h-7 text-primary-foreground" />
          </Button>
        </Link>
      </div>

      <AnimatePresence>
        {deleteConfirmation.show && (
          <ModernDeleteDialog
            isOpen={deleteConfirmation.show}
            onClose={() =>
              setDeleteConfirmation({
                show: false,
                sessionId: "",
                sessionInfo: "",
              })
            }
            onConfirm={confirmDeleteSession}
            sessionInfo={deleteConfirmation.sessionInfo || ""}
          />
        )}
      </AnimatePresence>
    </>
  );
}
